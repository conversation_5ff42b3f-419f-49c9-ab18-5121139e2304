import torch
import torch.nn as nn
import torch.nn.functional as F
import math


class SimplifiedFourierFilter(nn.Module):
    """
    简化傅里叶滤波器算子 - 结构化的频域调整
    
    使用高斯掩码进行频域滤波，比VPTTA的密集掩码更可解释
    """
    
    def __init__(self):
        super().__init__()
    
    def _generate_gaussian_mask(self, H, W, center_freq, std_dev, device):
        """
        生成高斯频域掩码
        
        Args:
            H (int): 图像高度
            W (int): 图像宽度
            center_freq (torch.Tensor): 中心频率 (B, 1)
            std_dev (torch.Tensor): 标准差 (B, 1)
            device: 设备
            
        Returns:
            torch.Tensor: 高斯掩码 (B, H, W)
        """
        B = center_freq.size(0)
        
        # 创建频率网格
        y_coords = torch.arange(H, dtype=torch.float32, device=device) - H // 2
        x_coords = torch.arange(W, dtype=torch.float32, device=device) - W // 2
        
        # 归一化频率坐标到[0, 1]
        y_coords = y_coords / (H // 2)
        x_coords = x_coords / (W // 2)
        
        # 创建网格
        Y, X = torch.meshgrid(y_coords, x_coords, indexing='ij')
        
        # 计算到中心的距离
        freq_distance = torch.sqrt(X**2 + Y**2)  # (H, W)
        
        # 扩展到batch维度
        freq_distance = freq_distance.unsqueeze(0).expand(B, -1, -1)  # (B, H, W)
        
        # 扩展参数维度
        center_freq = center_freq.unsqueeze(-1)  # (B, 1, 1)
        std_dev = std_dev.unsqueeze(-1)          # (B, 1, 1)
        
        # 生成高斯掩码
        # 低通滤波器：中心频率附近保留，远离中心频率的衰减
        mask = torch.exp(-0.5 * ((freq_distance - center_freq) / std_dev)**2)
        
        return mask
    
    def forward(self, x, freq_params):
        """
        前向传播：应用简化傅里叶滤波
        
        Args:
            x (torch.Tensor): 输入图像 (B, 3, H, W)
            freq_params (torch.Tensor): 频域参数 (B, 2) -> [center_freq, std_dev]
            
        Returns:
            torch.Tensor: 滤波后的图像 (B, 3, H, W)
        """
        B, C, H, W = x.shape
        
        # 分割参数
        center_freq = freq_params[:, 0:1]  # (B, 1)
        std_dev = freq_params[:, 1:2]      # (B, 1)
        
        # 参数约束
        center_freq = torch.sigmoid(center_freq) * 0.5      # 中心频率 [0, 0.5]
        std_dev = torch.sigmoid(std_dev) * 0.3 + 0.05       # 标准差 [0.05, 0.35]
        
        # 生成高斯掩码
        mask = self._generate_gaussian_mask(H, W, center_freq, std_dev, x.device)  # (B, H, W)
        
        # 对每个通道分别处理
        result_channels = []
        for c in range(C):
            x_channel = x[:, c, :, :]  # (B, H, W)
            
            # FFT变换
            fft = torch.fft.fft2(x_channel, dim=(-2, -1))
            fft_shifted = torch.fft.fftshift(fft)
            
            # 应用高斯掩码
            fft_filtered = fft_shifted * mask
            
            # 逆变换
            fft_ishifted = torch.fft.ifftshift(fft_filtered)
            result_channel = torch.fft.ifft2(fft_ishifted, dim=(-2, -1)).real
            
            result_channels.append(result_channel.unsqueeze(1))  # (B, 1, H, W)
        
        # 拼接所有通道
        result = torch.cat(result_channels, dim=1)  # (B, 3, H, W)
        
        return result


if __name__ == "__main__":
    # 测试简化傅里叶滤波器
    fourier_op = SimplifiedFourierFilter()
    x = torch.rand(2, 3, 64, 64)
    freq_params = torch.randn(2, 2)
    
    result_fourier = fourier_op(x, freq_params)
    print(f"Simplified Fourier filter - Input: {x.shape}, Output: {result_fourier.shape}")
    
    # 显示参数范围
    center_freq = torch.sigmoid(freq_params[:, 0:1]) * 0.5
    std_dev = torch.sigmoid(freq_params[:, 1:2]) * 0.3 + 0.05
    print(f"Center frequency range: {center_freq.flatten()}")
    print(f"Standard deviation range: {std_dev.flatten()}")
    
    # 验证输出值范围
    print(f"Input range: [{x.min():.3f}, {x.max():.3f}]")
    print(f"Fourier output range: [{result_fourier.min():.3f}, {result_fourier.max():.3f}]")
    
    # 测试梯度流
    x.requires_grad_(True)
    freq_params.requires_grad_(True)
    
    output = fourier_op(x, freq_params)
    loss = output.sum()
    loss.backward()
    
    print(f"Input gradient norm: {x.grad.norm():.6f}")
    print(f"Freq params gradient norm: {freq_params.grad.norm():.6f}")
    print("Gradient flow test passed!")
