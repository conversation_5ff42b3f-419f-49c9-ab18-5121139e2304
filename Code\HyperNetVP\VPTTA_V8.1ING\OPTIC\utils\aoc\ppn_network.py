import torch
import torch.nn as nn
import torch.nn.functional as F


class ParameterPredictionNetwork(nn.Module):
    """
    参数预测网络 - 轻量级CNN编码器预测算子参数
    
    架构：
    输入图像(B,3,H,W) → 轻量级编码器 → 参数预测头 → 19个算子参数
    """
    
    def __init__(self):
        """
        初始化参数预测网络
        """
        super().__init__()
        
        # 轻量级编码器
        self.encoder = nn.Sequential(
            # 第一层：3 → 32, 下采样4倍
            nn.Conv2d(3, 32, 7, stride=4, padding=3),
            nn.InstanceNorm2d(32),
            nn.LeakyReLU(0.2, inplace=True),
            
            # 第二层：32 → 64, 下采样4倍
            nn.Conv2d(32, 64, 5, stride=4, padding=2),
            nn.InstanceNorm2d(64),
            nn.LeakyReLU(0.2, inplace=True),
            
            # 第三层：64 → 128, 下采样2倍
            nn.Conv2d(64, 128, 3, stride=2, padding=1),
            nn.InstanceNorm2d(128),
            nn.LeakyReLU(0.2, inplace=True),
            
            # 全局平均池化
            nn.AdaptiveAvgPool2d(1, 1)
        )
        
        # 参数预测头
        self.param_head = nn.Sequential(
            nn.Linear(128, 256),
            nn.ReLU(inplace=True),
            nn.Linear(256, 19)  # 总参数数量：1+4+3+9+2=19
        )
        
        # 权重初始化
        self._initialize_weights()
    
    def _initialize_weights(self):
        """权重初始化"""
        for m in self.modules():
            if isinstance(m, nn.Conv2d):
                nn.init.kaiming_normal_(m.weight, mode='fan_out', nonlinearity='leaky_relu')
                if m.bias is not None:
                    nn.init.zeros_(m.bias)
            elif isinstance(m, nn.Linear):
                nn.init.xavier_uniform_(m.weight)
                if m.bias is not None:
                    # 最后一层偏置初始化为小值，其他层为0
                    if m.out_features == 19:
                        nn.init.normal_(m.bias, mean=0.0, std=0.01)
                    else:
                        nn.init.zeros_(m.bias)
            elif isinstance(m, nn.InstanceNorm2d):
                if m.weight is not None:
                    nn.init.ones_(m.weight)
                if m.bias is not None:
                    nn.init.zeros_(m.bias)
    
    def forward(self, x):
        """
        前向传播：预测算子参数
        
        Args:
            x (torch.Tensor): 输入图像 (B, 3, H, W)
            
        Returns:
            torch.Tensor: 算子参数 (B, 19)
        """
        # 编码器提取特征
        features = self.encoder(x)  # (B, 128, 1, 1)
        features = features.view(features.size(0), -1)  # (B, 128)
        
        # 预测参数
        params = self.param_head(features)  # (B, 19)
        
        return params
    
    def get_parameter_count(self):
        """
        获取网络参数统计
        
        Returns:
            dict: 参数统计信息
        """
        total_params = sum(p.numel() for p in self.parameters())
        trainable_params = sum(p.numel() for p in self.parameters() if p.requires_grad)
        
        # 分层统计
        encoder_params = sum(p.numel() for p in self.encoder.parameters())
        head_params = sum(p.numel() for p in self.param_head.parameters())
        
        return {
            'total_params': total_params,
            'trainable_params': trainable_params,
            'encoder_params': encoder_params,
            'head_params': head_params
        }


if __name__ == "__main__":
    # 测试PPN网络
    ppn = ParameterPredictionNetwork()
    
    # 测试前向传播
    x = torch.randn(2, 3, 512, 512)
    params = ppn(x)
    
    print(f"Input shape: {x.shape}")
    print(f"Output params shape: {params.shape}")
    print(f"Parameter statistics: {ppn.get_parameter_count()}")
