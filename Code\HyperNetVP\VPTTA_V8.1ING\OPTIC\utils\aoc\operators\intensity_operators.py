import torch
import torch.nn as nn
import torch.nn.functional as F


class GammaCorrection(nn.Module):
    """
    伽马校正算子 - 非线性对比度调整
    
    公式：X_out = X_in^γ
    参数：γ ∈ [0.3, 3.0]
    """
    
    def __init__(self):
        super().__init__()
    
    def forward(self, x, gamma):
        """
        前向传播：应用伽马校正
        
        Args:
            x (torch.Tensor): 输入图像 (B, 3, H, W)
            gamma (torch.Tensor): 伽马参数 (B, 1)
            
        Returns:
            torch.Tensor: 伽马校正后的图像 (B, 3, H, W)
        """
        # 将gamma约束到合理范围[0.3, 3.0]
        gamma = torch.sigmoid(gamma) * 2.7 + 0.3  # (B, 1)
        
        # 扩展维度以匹配图像尺寸
        gamma = gamma.unsqueeze(-1).unsqueeze(-1)  # (B, 1, 1, 1)
        
        # 确保输入值为正数，避免负数的幂运算
        x_clamped = x.clamp(min=1e-8)
        
        # 应用伽马校正
        result = torch.pow(x_clamped, gamma)
        
        return result


class WindowLevel(nn.Module):
    """
    窗宽窗位变换算子 - 线性对比度和亮度调整
    
    公式：X_out = L + W * sigmoid((X_in - C) / S)
    参数：W(窗宽), L(窗位), C(中心), S(锐度)
    """
    
    def __init__(self):
        super().__init__()
    
    def forward(self, x, params):
        """
        前向传播：应用窗宽窗位变换
        
        Args:
            x (torch.Tensor): 输入图像 (B, 3, H, W)
            params (torch.Tensor): 窗宽窗位参数 (B, 4) -> [W, L, C, S]
            
        Returns:
            torch.Tensor: 变换后的图像 (B, 3, H, W)
        """
        # 分割参数
        W, L, C, S = params.chunk(4, dim=1)  # 每个都是 (B, 1)
        
        # 参数约束
        W = torch.sigmoid(W) * 2.0 + 0.1      # 窗宽 [0.1, 2.1]
        L = torch.sigmoid(L) * 2.0 - 1.0      # 窗位 [-1.0, 1.0]
        C = torch.sigmoid(C)                  # 中心 [0.0, 1.0]
        S = torch.sigmoid(S) * 10.0 + 0.1     # 锐度 [0.1, 10.1]
        
        # 扩展维度以匹配图像尺寸
        W = W.unsqueeze(-1).unsqueeze(-1)  # (B, 1, 1, 1)
        L = L.unsqueeze(-1).unsqueeze(-1)  # (B, 1, 1, 1)
        C = C.unsqueeze(-1).unsqueeze(-1)  # (B, 1, 1, 1)
        S = S.unsqueeze(-1).unsqueeze(-1)  # (B, 1, 1, 1)
        
        # 应用窗宽窗位变换
        result = L + W * torch.sigmoid((x - C) / S)
        
        return result


if __name__ == "__main__":
    # 测试伽马校正
    gamma_op = GammaCorrection()
    x = torch.rand(2, 3, 64, 64)
    gamma_params = torch.randn(2, 1)
    
    result_gamma = gamma_op(x, gamma_params)
    print(f"Gamma correction - Input: {x.shape}, Output: {result_gamma.shape}")
    print(f"Gamma range: {torch.sigmoid(gamma_params) * 2.7 + 0.3}")
    
    # 测试窗宽窗位
    window_op = WindowLevel()
    window_params = torch.randn(2, 4)
    
    result_window = window_op(x, window_params)
    print(f"Window/Level - Input: {x.shape}, Output: {result_window.shape}")
    
    # 验证输出值范围
    print(f"Input range: [{x.min():.3f}, {x.max():.3f}]")
    print(f"Gamma output range: [{result_gamma.min():.3f}, {result_gamma.max():.3f}]")
    print(f"Window output range: [{result_window.min():.3f}, {result_window.max():.3f}]")
