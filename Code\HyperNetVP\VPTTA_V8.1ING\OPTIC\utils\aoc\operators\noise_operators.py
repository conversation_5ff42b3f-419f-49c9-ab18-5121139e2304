import torch
import torch.nn as nn
import torch.nn.functional as F


class AnisotropicDiffusion(nn.Module):
    """
    各向异性扩散算子 - 边缘保护的噪声平滑
    
    执行3次迭代的各向异性扩散，每次使用不同的扩散系数
    """
    
    def __init__(self):
        super().__init__()
    
    def _compute_laplacian(self, x):
        """
        计算拉普拉斯算子（简化版本）
        
        Args:
            x (torch.Tensor): 输入图像 (B, C, H, W)
            
        Returns:
            torch.Tensor: 拉普拉斯结果 (B, C, H, W)
        """
        # 定义拉普拉斯卷积核
        laplacian_kernel = torch.tensor([
            [0, -1, 0],
            [-1, 4, -1],
            [0, -1, 0]
        ], dtype=x.dtype, device=x.device).view(1, 1, 3, 3)
        
        # 对每个通道分别应用拉普拉斯算子
        B, C, H, W = x.shape
        x_reshaped = x.view(B * C, 1, H, W)
        
        laplacian = F.conv2d(x_reshaped, laplacian_kernel, padding=1)
        laplacian = laplacian.view(B, C, H, W)
        
        return laplacian
    
    def _compute_gradient_magnitude(self, x):
        """
        计算梯度幅度用于边缘检测
        
        Args:
            x (torch.Tensor): 输入图像 (B, C, H, W)
            
        Returns:
            torch.Tensor: 梯度幅度 (B, 1, H, W)
        """
        # Sobel算子
        sobel_x = torch.tensor([
            [-1, 0, 1],
            [-2, 0, 2],
            [-1, 0, 1]
        ], dtype=x.dtype, device=x.device).view(1, 1, 3, 3)
        
        sobel_y = torch.tensor([
            [-1, -2, -1],
            [0, 0, 0],
            [1, 2, 1]
        ], dtype=x.dtype, device=x.device).view(1, 1, 3, 3)
        
        # 计算每个通道的梯度，然后取平均
        B, C, H, W = x.shape
        grad_x_total = 0
        grad_y_total = 0
        
        for c in range(C):
            x_channel = x[:, c:c+1, :, :]  # (B, 1, H, W)
            grad_x = F.conv2d(x_channel, sobel_x, padding=1)
            grad_y = F.conv2d(x_channel, sobel_y, padding=1)
            grad_x_total += grad_x
            grad_y_total += grad_y
        
        # 平均梯度幅度
        grad_magnitude = torch.sqrt(
            (grad_x_total / C).pow(2) + (grad_y_total / C).pow(2) + 1e-8
        )
        
        return grad_magnitude
    
    def forward(self, x, diffusion_coeffs):
        """
        前向传播：应用各向异性扩散
        
        Args:
            x (torch.Tensor): 输入图像 (B, 3, H, W)
            diffusion_coeffs (torch.Tensor): 扩散系数 (B, 3) - 3次迭代
            
        Returns:
            torch.Tensor: 扩散后的图像 (B, 3, H, W)
        """
        # 约束扩散系数到合理范围[0, 0.2]
        coeffs = torch.sigmoid(diffusion_coeffs) * 0.2  # (B, 3)
        
        result = x
        
        # 执行3次迭代扩散
        for i in range(3):
            # 获取当前迭代的扩散系数
            coeff = coeffs[:, i:i+1].unsqueeze(-1).unsqueeze(-1)  # (B, 1, 1, 1)
            
            # 计算梯度幅度用于边缘检测
            grad_mag = self._compute_gradient_magnitude(result)  # (B, 1, H, W)
            
            # 边缘保护权重：在边缘处扩散较少
            edge_threshold = 0.1
            diffusion_weight = torch.exp(-grad_mag / edge_threshold)  # (B, 1, H, W)
            
            # 计算拉普拉斯算子
            laplacian = self._compute_laplacian(result)  # (B, 3, H, W)
            
            # 应用各向异性扩散
            # 在边缘处（高梯度）扩散权重小，在平滑区域扩散权重大
            result = result + coeff * diffusion_weight * laplacian
            
            # 确保值在合理范围内
            result = torch.clamp(result, 0.0, 1.0)
        
        return result


class ConvolutionalFilter(nn.Module):
    """
    卷积滤波器算子 - 使用可学习的3×3卷积核
    
    支持锐化、模糊、边缘检测等多种局部线性变换
    """
    
    def __init__(self):
        super().__init__()
    
    def forward(self, x, kernel_params):
        """
        前向传播：应用卷积滤波
        
        Args:
            x (torch.Tensor): 输入图像 (B, 3, H, W)
            kernel_params (torch.Tensor): 卷积核参数 (B, 9) - 3×3卷积核权重
            
        Returns:
            torch.Tensor: 滤波后的图像 (B, 3, H, W)
        """
        B, C, H, W = x.shape
        
        # 将参数重塑为3×3卷积核
        kernels = kernel_params.view(B, 1, 3, 3)  # (B, 1, 3, 3)
        
        # 对每个样本分别应用卷积
        results = []
        for i in range(B):
            # 对每个通道应用相同的卷积核
            result_channels = []
            for c in range(C):
                conv_result = F.conv2d(
                    x[i:i+1, c:c+1],  # (1, 1, H, W)
                    kernels[i:i+1],   # (1, 1, 3, 3)
                    padding=1
                )
                result_channels.append(conv_result)
            
            # 拼接所有通道
            sample_result = torch.cat(result_channels, dim=1)  # (1, 3, H, W)
            results.append(sample_result)
        
        # 拼接所有样本
        result = torch.cat(results, dim=0)  # (B, 3, H, W)
        
        return result


if __name__ == "__main__":
    # 测试各向异性扩散
    diffusion_op = AnisotropicDiffusion()
    x = torch.rand(2, 3, 64, 64)
    diffusion_params = torch.randn(2, 3)
    
    result_diffusion = diffusion_op(x, diffusion_params)
    print(f"Anisotropic diffusion - Input: {x.shape}, Output: {result_diffusion.shape}")
    print(f"Diffusion coeffs range: {torch.sigmoid(diffusion_params) * 0.2}")
    
    # 测试卷积滤波器
    conv_op = ConvolutionalFilter()
    conv_params = torch.randn(2, 9)
    
    result_conv = conv_op(x, conv_params)
    print(f"Convolutional filter - Input: {x.shape}, Output: {result_conv.shape}")
    
    # 验证输出值范围
    print(f"Input range: [{x.min():.3f}, {x.max():.3f}]")
    print(f"Diffusion output range: [{result_diffusion.min():.3f}, {result_diffusion.max():.3f}]")
    print(f"Conv output range: [{result_conv.min():.3f}, {result_conv.max():.3f}]")
