import torch
import torch.nn as nn
from .ppn_network import ParameterPredictionNetwork
from .operator_pool import IntensityLayer, NoiseLayer, FrequencyLayer


class AOCGenerator(nn.Module):
    """
    自适应算子组合生成器 - AOC-TTA框架的核心组件
    
    架构流程：
    1. 输入图像 → PPN → 算子参数
    2. 分层算子组合：强度调整 → 噪声处理 → 频域调整
    3. 输出自适应后的图像
    """
    
    def __init__(self, ppn_lr=0.001, param_reg_weight=0.01):
        """
        初始化AOC生成器
        
        Args:
            ppn_lr (float): PPN学习率（用于记录，实际优化器在外部创建）
            param_reg_weight (float): 参数正则化权重
        """
        super().__init__()
        
        # 参数预测网络
        self.ppn = ParameterPredictionNetwork()
        
        # 三层算子池
        self.intensity_layer = IntensityLayer()
        self.noise_layer = NoiseLayer()
        self.frequency_layer = FrequencyLayer()
        
        # 超参数
        self.ppn_lr = ppn_lr
        self.param_reg_weight = param_reg_weight
        
        # 参数分割索引（总共19个参数）
        self.param_splits = {
            'gamma': (0, 1),        # 伽马校正：1个参数
            'window': (1, 5),       # 窗宽窗位：4个参数
            'diffusion': (5, 8),    # 各向异性扩散：3个参数
            'conv': (8, 17),        # 卷积滤波：9个参数
            'freq': (17, 19)        # 频域滤波：2个参数
        }
    
    def _split_parameters(self, all_params):
        """
        分割PPN输出的参数到各个算子
        
        Args:
            all_params (torch.Tensor): PPN输出的所有参数 (B, 19)
            
        Returns:
            dict: 分割后的参数字典
        """
        params_dict = {}
        for name, (start, end) in self.param_splits.items():
            params_dict[name] = all_params[:, start:end]
        
        return params_dict
    
    def forward(self, x):
        """
        前向传播：生成自适应图像
        
        Args:
            x (torch.Tensor): 输入图像 (B, 3, H, W)
            
        Returns:
            tuple: (adapted_image, operator_params)
                adapted_image (torch.Tensor): 自适应后的图像 (B, 3, H, W)
                operator_params (torch.Tensor): 算子参数 (B, 19)
        """
        # 1. 预测所有算子参数
        all_params = self.ppn(x)  # (B, 19)
        
        # 2. 分割参数
        params_dict = self._split_parameters(all_params)
        
        # 3. 三层算子组合
        # 第一层：强度与对比度调整
        x_adapted = self.intensity_layer(
            x, 
            params_dict['gamma'], 
            params_dict['window']
        )
        
        # 第二层：噪声与纹理处理
        x_adapted = self.noise_layer(
            x_adapted,
            params_dict['diffusion'],
            params_dict['conv']
        )
        
        # 第三层：频域调整
        x_adapted = self.frequency_layer(
            x_adapted,
            params_dict['freq']
        )
        
        return x_adapted, all_params
    
    def get_regularization_loss(self, params):
        """
        计算参数正则化损失
        
        Args:
            params (torch.Tensor): 算子参数 (B, 19)
            
        Returns:
            torch.Tensor: 正则化损失标量
        """
        return self.param_reg_weight * torch.norm(params, p=2, dim=1).mean()
    
    def get_parameter_stats(self):
        """
        获取网络参数统计信息
        
        Returns:
            dict: 参数统计
        """
        ppn_stats = self.ppn.get_parameter_count()
        
        # 算子层没有可训练参数，只是组合现有算子
        total_params = ppn_stats['total_params']
        
        return {
            'total_params': total_params,
            'trainable_params': total_params,  # 只有PPN可训练
            'ppn_stats': ppn_stats,
            'operator_param_count': 19,  # 输出的算子参数数量
            'param_splits': self.param_splits
        }
    
    def get_operator_interpretation(self, params):
        """
        获取算子参数的可解释信息
        
        Args:
            params (torch.Tensor): 算子参数 (B, 19)
            
        Returns:
            dict: 可解释的参数信息
        """
        params_dict = self._split_parameters(params)
        
        # 将原始参数转换为实际使用的参数值
        interpretation = {}
        
        # 伽马校正参数
        gamma_actual = torch.sigmoid(params_dict['gamma']) * 2.7 + 0.3
        interpretation['gamma_correction'] = {
            'gamma_values': gamma_actual.detach().cpu().numpy(),
            'description': 'Gamma values for nonlinear contrast adjustment'
        }
        
        # 窗宽窗位参数
        W = torch.sigmoid(params_dict['window'][:, 0:1]) * 2.0 + 0.1
        L = torch.sigmoid(params_dict['window'][:, 1:2]) * 2.0 - 1.0
        C = torch.sigmoid(params_dict['window'][:, 2:3])
        S = torch.sigmoid(params_dict['window'][:, 3:4]) * 10.0 + 0.1
        interpretation['window_level'] = {
            'window_width': W.detach().cpu().numpy(),
            'window_level': L.detach().cpu().numpy(),
            'center': C.detach().cpu().numpy(),
            'sharpness': S.detach().cpu().numpy(),
            'description': 'Window/Level parameters for linear contrast adjustment'
        }
        
        # 扩散参数
        diffusion_actual = torch.sigmoid(params_dict['diffusion']) * 0.2
        interpretation['anisotropic_diffusion'] = {
            'diffusion_coefficients': diffusion_actual.detach().cpu().numpy(),
            'description': 'Diffusion coefficients for 3 iterations of edge-preserving smoothing'
        }
        
        # 卷积核参数
        interpretation['convolution_filter'] = {
            'kernel_weights': params_dict['conv'].detach().cpu().numpy(),
            'description': '3x3 convolution kernel weights for local filtering'
        }
        
        # 频域参数
        center_freq = torch.sigmoid(params_dict['freq'][:, 0:1]) * 0.5
        std_dev = torch.sigmoid(params_dict['freq'][:, 1:2]) * 0.3 + 0.05
        interpretation['fourier_filter'] = {
            'center_frequency': center_freq.detach().cpu().numpy(),
            'standard_deviation': std_dev.detach().cpu().numpy(),
            'description': 'Gaussian mask parameters for frequency domain filtering'
        }
        
        return interpretation


if __name__ == "__main__":
    # 测试AOC生成器
    aoc_generator = AOCGenerator(ppn_lr=0.001, param_reg_weight=0.01)
    
    # 测试前向传播
    x = torch.rand(2, 3, 512, 512)
    adapted_x, params = aoc_generator(x)
    
    print(f"Input shape: {x.shape}")
    print(f"Adapted image shape: {adapted_x.shape}")
    print(f"Operator parameters shape: {params.shape}")
    
    # 测试正则化损失
    reg_loss = aoc_generator.get_regularization_loss(params)
    print(f"Regularization loss: {reg_loss.item():.6f}")
    
    # 获取参数统计
    stats = aoc_generator.get_parameter_stats()
    print(f"\nParameter statistics:")
    for key, value in stats.items():
        if key != 'ppn_stats':
            print(f"  {key}: {value}")
    
    # 获取可解释信息
    interpretation = aoc_generator.get_operator_interpretation(params)
    print(f"\nOperator interpretation (first sample):")
    for op_name, op_info in interpretation.items():
        print(f"  {op_name}: {op_info['description']}")
        for param_name, param_values in op_info.items():
            if param_name != 'description':
                if hasattr(param_values, 'shape'):
                    print(f"    {param_name}: shape {param_values.shape}")
                else:
                    print(f"    {param_name}: {param_values}")
    
    # 验证梯度流
    x.requires_grad_(True)
    adapted_x, params = aoc_generator(x)
    loss = adapted_x.sum() + aoc_generator.get_regularization_loss(params)
    loss.backward()
    
    print(f"\nGradient flow test:")
    print(f"Input gradient norm: {x.grad.norm():.6f}")
    print(f"PPN parameter gradient norms:")
    for name, param in aoc_generator.ppn.named_parameters():
        if param.grad is not None:
            print(f"  {name}: {param.grad.norm():.6f}")
    
    print("AOC Generator test completed successfully!")
