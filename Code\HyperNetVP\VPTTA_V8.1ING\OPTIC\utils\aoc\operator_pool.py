import torch
import torch.nn as nn
from .operators.intensity_operators import GammaCorrection, WindowLevel
from .operators.noise_operators import AnisotropicDiffusion, ConvolutionalFilter
from .operators.frequency_operators import SimplifiedFourierFilter


class IntensityLayer(nn.Module):
    """
    强度与对比度调整层
    
    包含：伽马校正 + 窗宽窗位变换
    参数：1 + 4 = 5个参数
    """
    
    def __init__(self):
        super().__init__()
        self.gamma_correction = GammaCorrection()
        self.window_level = WindowLevel()
    
    def forward(self, x, gamma_params, window_params):
        """
        前向传播：应用强度调整
        
        Args:
            x (torch.Tensor): 输入图像 (B, 3, H, W)
            gamma_params (torch.Tensor): 伽马参数 (B, 1)
            window_params (torch.Tensor): 窗宽窗位参数 (B, 4)
            
        Returns:
            torch.Tensor: 强度调整后的图像 (B, 3, H, W)
        """
        # 先应用伽马校正
        x_gamma = self.gamma_correction(x, gamma_params)
        
        # 再应用窗宽窗位变换
        x_window = self.window_level(x_gamma, window_params)
        
        return x_window


class NoiseLayer(nn.Module):
    """
    噪声与纹理处理层
    
    包含：各向异性扩散 + 卷积滤波
    参数：3 + 9 = 12个参数
    """
    
    def __init__(self):
        super().__init__()
        self.anisotropic_diffusion = AnisotropicDiffusion()
        self.convolutional_filter = ConvolutionalFilter()
    
    def forward(self, x, diffusion_params, conv_params):
        """
        前向传播：应用噪声处理
        
        Args:
            x (torch.Tensor): 输入图像 (B, 3, H, W)
            diffusion_params (torch.Tensor): 扩散参数 (B, 3)
            conv_params (torch.Tensor): 卷积参数 (B, 9)
            
        Returns:
            torch.Tensor: 噪声处理后的图像 (B, 3, H, W)
        """
        # 先应用各向异性扩散
        x_diffusion = self.anisotropic_diffusion(x, diffusion_params)
        
        # 再应用卷积滤波
        x_conv = self.convolutional_filter(x_diffusion, conv_params)
        
        return x_conv


class FrequencyLayer(nn.Module):
    """
    频域调整层
    
    包含：简化傅里叶滤波
    参数：2个参数
    """
    
    def __init__(self):
        super().__init__()
        self.fourier_filter = SimplifiedFourierFilter()
    
    def forward(self, x, freq_params):
        """
        前向传播：应用频域调整
        
        Args:
            x (torch.Tensor): 输入图像 (B, 3, H, W)
            freq_params (torch.Tensor): 频域参数 (B, 2)
            
        Returns:
            torch.Tensor: 频域调整后的图像 (B, 3, H, W)
        """
        # 应用简化傅里叶滤波
        x_freq = self.fourier_filter(x, freq_params)
        
        return x_freq


if __name__ == "__main__":
    # 测试三个算子层
    intensity_layer = IntensityLayer()
    noise_layer = NoiseLayer()
    frequency_layer = FrequencyLayer()
    
    # 创建测试数据
    x = torch.rand(2, 3, 64, 64)
    
    # 测试强度层
    gamma_params = torch.randn(2, 1)
    window_params = torch.randn(2, 4)
    x_intensity = intensity_layer(x, gamma_params, window_params)
    print(f"Intensity layer - Input: {x.shape}, Output: {x_intensity.shape}")
    
    # 测试噪声层
    diffusion_params = torch.randn(2, 3)
    conv_params = torch.randn(2, 9)
    x_noise = noise_layer(x_intensity, diffusion_params, conv_params)
    print(f"Noise layer - Input: {x_intensity.shape}, Output: {x_noise.shape}")
    
    # 测试频域层
    freq_params = torch.randn(2, 2)
    x_freq = frequency_layer(x_noise, freq_params)
    print(f"Frequency layer - Input: {x_noise.shape}, Output: {x_freq.shape}")
    
    # 验证完整的三层组合
    print(f"\nComplete pipeline:")
    print(f"Original -> Intensity -> Noise -> Frequency")
    print(f"{x.shape} -> {x_intensity.shape} -> {x_noise.shape} -> {x_freq.shape}")
    
    # 验证参数总数
    total_params = 1 + 4 + 3 + 9 + 2
    print(f"\nTotal operator parameters: {total_params}")
    print(f"Gamma: 1, Window: 4, Diffusion: 3, Conv: 9, Freq: 2")
    
    # 验证输出值范围
    print(f"\nValue ranges:")
    print(f"Original: [{x.min():.3f}, {x.max():.3f}]")
    print(f"After intensity: [{x_intensity.min():.3f}, {x_intensity.max():.3f}]")
    print(f"After noise: [{x_noise.min():.3f}, {x_noise.max():.3f}]")
    print(f"After frequency: [{x_freq.min():.3f}, {x_freq.max():.3f}]")
